<!DOCTYPE html>
<html lang="zh-CN"><!-- 格式调用部分 -->

<head>
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="theme-color" content="#000">

	<meta http-equiv="X-UA-Compatible" content="IE=Edge">
	<meta name="viewport" id="viewport" content="width=device-width, initial-scale=1">
	<title>MyHomepage</title><!-- 头文字标题1 -->
	<meta name="description" content="xiadengma的个人网站"><!-- 头文字标题2 -->
	<meta name="keywords" content="xiadengma 下等马 个人网站 博客"><!-- 搜索引擎关键字 -->
	<link rel="icon" href="./files/favicon.svg" media="(prefers-color-scheme: light)"><!-- 浅色模式favicon -->
	<link rel="icon" href="./files/favicon_drak.svg" media="(prefers-color-scheme: dark)"><!-- 深色模式favicon -->
	<link href="./files/h.css" rel="stylesheet"><!-- 主css调用 -->
	<link rel="stylesheet" type="text/css" href="./files/nekotora.css">
	<!-- 网页统计代码 -->
	<script defer src="https://cloud.umami.is/script.js"
		data-website-id="89c779dd-253e-410a-9b19-779156fcc80d"></script>
</head>
<!-- 格式调用部分 END -->
<!-- 内容部分 -->

<body data-instant-mousedown-shortcut>
	<!-- 侧边导航 -->
	<nav>
		<a class="active english-text">Homepage</a>
		<a class="clip"></a>
		<a href="https://blog.xiadengma.com/" class="time english-text" target="_blank"
			rel="noopener noreferrer">Blog</a>
	</nav>
	<!-- 侧边导航 END -->
	<!-- 自适应调节 -->
	<div class="background">
		<canvas id="startrack" width="1708" height="1304"></canvas>
		<div class="cover"></div>
	</div><!-- 自适应调节 END -->
	<!-- 内容部分 -->
	<div class="main">
		<!-- 开头部分 -->
		<div class="ch intro">
			<div class="container">
				<!-- 开头文字部分1 -->
				<div class="hello">
					<h1 id="slogan" class="chinese-text">当你在凝视着网页的时候<br>网页也正在凝视着你</h1><!-- 随机文字 -->
					<div class="kong"></div>
					<span class="toppic-line"></span>
					<!-- 开头文字部分2 -->
					<h2>
						<div class="circle">
							<span></span><!-- 红点 -->
							<span></span><!-- 黄点 -->
							<span></span><!-- 绿点 -->
						</div>
						<div class="english-text">Welcome To My Homepage
						</div>
					</h2><!-- 开头文字部分2 END -->
				</div><!-- 开头文字部分 END -->
			</div>
		</div>
		<!-- 开头部分 END -->

		<!-- 简介部分 -->
		<div class="ch about">
			<div class="container">
				<h2 class="b chtitle english-text">
					<span class="b english-text">Who</span> am I ？
				</h2>
				<div class="clear">
					<div class="introduct">
						<img class="avatar" src="./files/avatar.webp"><!-- 头像图标调用 -->
						<div class="kongk"></div>
						<!-- 文字部分 -->
						<p class="chinese-text">你好！这里是
							<span class="b english-text">xiadengma<sup class="english-text">@下等马</sup></span> ，
							<span id="age" class="english-text"></span>岁。
						</p>
						<p class="chinese-text>">热爱科技、艺术、美食和探索未知，享受安静独处的时间，对浩瀚的世界和你感到好奇。 </p>
						<p style="padding-bottom:1em" class="chinese-text"></p>
						<p style="padding-bottom:1em">
							<span class="chinese-text">如果对我感兴趣，可以</span>
							<a href="javascript:rollOnce()" class="b chinese-text">点击这里</a>
							<span class="english-text">Roll</span>
							<span class="chinese-text">一个关于我的标签，也欢迎在下面的链接找我聊聊。</span>
						</p>
						<div class="roll-tag"></div>
						<script>
							function calculateAge(birthYear, birthMonth, birthDay) {
								const today = new Date();
								const birthDate = new Date(birthYear, birthMonth - 1, birthDay);
								let age = today.getFullYear() - birthDate.getFullYear();
								const m = today.getMonth() - birthDate.getMonth();
								if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
									age--;
								}
								return age;
							}

							function updateAge() {
								const birthYear = 2002;
								const birthMonth = 2;
								const birthDay = 27;

								const age = calculateAge(birthYear, birthMonth, birthDay);
								document.getElementById('age').textContent = age;
							}

							// 页面加载时更新年龄
							updateAge();

							// 每天更新一次年龄
							setInterval(updateAge, 24 * 60 * 60 * 1000);
						</script>
						<!-- 文字部分 END -->
						<!-- 社交链接部分 -->
						<div class="find-me">
							<a class="item" href="https://github.com/xiadengma" target="_blank">
								<i class="neko-icon icon-github"></i>
								<span class="english-text">GitHub</span>
							</a>
							<a class="item" href="https://t.me/xiadengma_9876" target="_blank">
								<i class="neko-icon icon-telegram"></i>
								<span class="english-text">TG</span>
							</a>
							<a class="item" href="mailto:<EMAIL>" target="_blank">
								<i class="neko-icon icon-email"></i>
								<span class="english-text">email</span>
							</a>
						</div>
						<!-- 社交链接部分 END -->
					</div>
				</div>
			</div>
		</div>
		<!-- 简介部分 END -->



		<!-- 结尾部分 -->
		<div class="footer ch">
			<div class="container">
				<div class="footer-bottom">
					<div class="footer-left">
						<p class="c">© <span id="currentYear"></span> <a href="https://xiadengma.com/">XIADENGMA</a> |
							All rights reserved | <a href="https://beian.miit.gov.cn/" target="_blank"
								rel="noopener noreferrer">浙ICP备20005376号-2</a>
						</p>
					</div>
					<div class="footer-right">
						<p>🛸Based on Nekotora</p>
					</div>
				</div>
			</div>
		</div>
		<!-- 结尾部分 END -->

		<script>
			// 获取当前年份并更新页面
			document.getElementById('currentYear').textContent = new Date().getFullYear();
		</script>
	</div>
	<!-- 内容部分 END -->

	<script src="https://cdn.jsdelivr.net/npm/instant.page/instantpage.min.js" type="module"></script><!-- 预加载 -->
	<script src="./files/zepto.min.js"></script>
	<script src="./files/page.js"></script><!-- 星空&随机文字&随机标签js调用 -->

</body><!-- 结尾运行时间 END --><!-- 内容部分 END -->

</html>
