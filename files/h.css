* {
	padding: 0;
	margin: 0;
	transition-timing-function: cubic-bezier(.19, 1, .22, 1);
}

:root {
	--font-en: 'MonoLisa Nerd Font';
	--font-cn: 'SF Pro Text';
}

@font-face {
	font-family: 'SF Pro Text';
	src: url('./SFProText-Regular.woff2') format('woff2');
	font-weight: normal;
	font-style: normal;
}

@font-face {
	font-family: 'MonoLisa Nerd Font';
	src: url('./MonoLisaNerdFont-Regular.woff2') format('woff2');
	font-weight: normal;
	font-style: normal;
}

body {
	font-family: var(--font-cn), var(--font-en), -apple-system, BlinkMacSystemFont, sans-serif;
	-webkit-font-smoothing: antialiased;
	color: #fff;
	line-height: 2;
	padding: 0 5%;
	background-color: #212121;
	font-size: 17px;
	overflow-x: hidden;
}

/* 为特定内容指定字体 */
.chinese-text {
	font-family: var(--font-cn), sans-serif;
}

.english-text {
	font-family: var(--font-en), sans-serif;
}


body,
html {
	height: 100%;
	width: 100%;
	font-family: var(--font-cn),
		var(--font-en),
		-apple-system,
		BlinkMacSystemFont,
		sans-serif;
	-webkit-font-smoothing: antialiased;
	color: #fff;
	line-height: 1.5em;
	min-width: 1150px;
	background-color: #212121;
	font-size: 14px;
	overflow-x: hidden
}

div.background {
	position: fixed;
	width: 100%;
	height: 100%;
	transition: .3s all;
	background-color: #252525;
}

::selection {
	background: rgba(255, 255, 255, .08);
}

.intro {
	position: relative;
}

.clear:after {
	content: "";
	display: block;
	height: 0;
	visibility: hidden;
	clear: both;
}

#startrack {
	height: 140%;
	width: 100%
}

.background.fixed {
	position: fixed;
	top: -60%
}

.background {
	position: absolute;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh
}

.background .cover {
	position: absolute;
	bottom: -42%;
	left: 0;
	height: 100%;
	width: 100%;
	background: linear-gradient(0deg, #202020 30%, rgba(32, 32, 32, 0))
}

nav {
	position: fixed;
	left: 20px;
	top: 0;
	z-index: 2333;
	transform: rotate(90deg) translate3d(0, 0, 0);
	transform-origin: 0% 100%;
	opacity: .7;
	transition: .3s all ease-out;
	font-family: var(--font-cn),
		var(--font-en),
		-apple-system,
		BlinkMacSystemFont,
		sans-serif;
}

nav a {
	display: inline-block;
	font-size: 15px;
	color: #fff;
	text-decoration: none;
	margin: 0 10px;
	opacity: .4;
	transition: .3s all;
}

nav a.time {
	opacity: 1;
}

nav a.clip {
	height: 3px;
	width: 3px;
	background: #fff;
	border-radius: 100%
}

nav a:hover,
nav a.active {
	text-decoration: underline;
	opacity: 1;
}

.main {
	position: relative;
	z-index: 1000;
	width: 100%;
	height: 100%;
}

.ch {
	width: 100%;
	padding: 80px 0;
	animation: fadedown 1s cubic-bezier(.19, 1, .22, 1);
	-webkit-animation: fadedown 1s cubic-bezier(.19, 1, .22, 1);
}

.ch h2.chtitle {
	padding-bottom: 30px;
	font-size: 26px;
	letter-spacing: .2em;
	color: rgba(255, 255, 255, .3);
}

.ch h2.chtitle span {
	color: #fff;
}

.ch h2.chtitle:after {
	content: '';
	display: block;
	width: 10%;
	height: 2px;
	background-color: rgba(255, 255, 255, .3);
	margin-top: 30px;
}

@keyframes fadedown {
	0% {
		opacity: 0;
		transform: translateY(-50px);
	}

	100% {
		opacity: 1;
		transform: translateY(0);
	}
}

.container {
	position: relative;
	width: 1000px;
	margin: 0 auto;
	height: 100%;
	padding: 20px 0;
}

.intro {
	color: #fff;
	height: 100%;
	padding: 0;
}

.intro .container {
	animation: fadedown 3.5s cubic-bezier(.19, 1, .22, 1);
	-webkit-animation: fadedown 3.5s cubic-bezier(.19, 1, .22, 1);
	-ms-animation: fadedown 3.5s cubic-bezier(.19, 1, .22, 1);
	-moz-animation: fadedown 3.5s cubic-bezier(.19, 1, .22, 1);
}

.hello {
	position: absolute;
	bottom: 20%;
	left: 0;
}

.hello h1,
.hello h2 {
	font-weight: 400;
	font-size: 30px;
	line-height: 1.5em;
	letter-spacing: .2em
}

.hello h1 {
	font-size: 42px;
	letter-spacing: .2em
}

.hello h2 {
	font-family: var(--font-cn),
		var(--font-en),
		-apple-system,
		BlinkMacSystemFont,
		sans-serif;
	padding-top: .2em;
}

.hello .circle {
	float: left;
	margin-right: 10px;
	letter-spacing: 0;
}

.hello .circle span {
	display: inline-block;
	width: 13px;
	height: 13px;
	background-color: #fff;
	border-radius: 100%;
	margin-right: 5px;
}

.hello .circle span:nth-child(1) {
	background-color: #FF493F;
}

.hello .circle span:nth-child(2) {
	background-color: #F7C900;
}

.hello .circle span:nth-child(3) {
	background-color: #00ff37;
}

.about .introduct {
	line-height: 2em;
}

.about .introduct img.avatar {
	float: right;
	width: 140px;
	margin-left: 40px;
	margin-right: 20px;
	border-radius: 100%
}

.about sup {
	font-size: .76em;
}

.about a {
	position: relative;
	color: #fff;
	margin: 0 5px;
	text-decoration: none;
	transition: .3s all;
}

.about a::after {
	content: "";
	position: absolute;
	bottom: -5px;
	left: 0;
	width: 95%;
	height: 3px;
	background-color: rgba(255, 255, 255, .6);
	transition: .1s all;
}

.about a:hover::after {
	background-color: rgba(255, 255, 255, 1);
	height: 5px;
}

.about .b {
	font-weight: bold;
}

.footer {
	text-align: center;
	width: 100%;
	padding: 20px 0 10px;
	position: relative;
	z-index: 1;
}

.footer .container {
	position: relative;
	height: auto;
	z-index: 10;
}

.footer h3 {
	font-weight: 400;
	font-size: 20px;
	letter-spacing: .2em;
	margin: 6px 0 3px;
}

.footer p {
	font-size: 12px;
	letter-spacing: 0.2em;
	opacity: .3;
	margin: 0;
}

.footer a {
	color: inherit;
	text-decoration: none;
}

.footer a:hover {
	text-decoration: underline;
}

.footer-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 10px;
}

.footer-left {
	text-align: left;
}

.footer-right {
	text-align: right;
}

.footer p.c {
	margin: 0;
}

.toppic-line {
	position: relative;
	bottom: 0;
	left: 0;
	display: block;
	width: 100%;
	height: 2px;
	background-color: #fff;
	animation: lineWidth 2.5s;
	animation-fill-mode: forwards;
}

.kong {
	padding-top: .6em;
}

.kongk {
	padding-top: .10em;
}

@keyframes lineWidth {
	0% {
		width: 0;
	}

	100% {
		width: 100%;
	}
}

/* 响应式布局 */
@media screen and (min-width: 1400px) {
	.container {
		width: 70%;
	}

	nav a {
		font-size: 18px;
	}
}

@media screen and (max-width: 700px) {

	body,
	html {
		min-width: 0;
	}

	.container {
		width: auto;
		margin: 0 5%;
	}

	nav {
		padding: 15px 5%;
		overflow: auto;
		font-size: 13px;
		left: 0;
		top: inherit;
		bottom: 0;
		transform: rotate(0deg) translate3d(0, 0, 0);
		transform-origin: 0% 100%;
		white-space: nowrap;
		width: 90%;
		background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, .8));
	}

	.ch {
		padding: 40px 0;
	}

	.hello {
		width: 90%;
		padding: 5%;
		text-align: center;
		bottom: 35%
	}

	.hello .circle {
		float: none;
		margin-right: 0;
	}

	.hello h2 {
		padding-top: 0;
	}

	.hello h1,
	.hello h2 {
		font-weight: 400;
		font-size: 22px
	}

	.hello .circle {
		padding: 20px;
	}

	.hello .circle span {
		margin: 0 5px;
		width: 10px;
		height: 10px
	}

	.hello h1 {
		font-size: 30px
	}

	.about .introduct img.avatar {
		float: none;
		margin-left: 0;
		margin-right: 0;
		width: 100px
	}

	.roll-tag {
		height: 60px;
	}

	.roll-tag span {
		white-space: normal;
		display: block;
	}

	.footer {
		padding: 15px 0 5px;
	}

	.footer-bottom {
		flex-direction: column;
		gap: 5px;
	}

	.footer-left,
	.footer-right {
		width: 100%;
		text-align: center;
	}
}

@media (max-width: 860px) {

	.kong,
	.toppic-line {
		display: none;
	}
}

@media (min-width: 860px) {
	.about .introduct {
		letter-spacing: .2em;
		font-size: 18px;
	}
}

.find-me a {
	text-decoration: none;
	color: #fff;
}

.find-me .item {
	display: inline-block;
	width: 80px;
	height: 80px;
	line-height: 80px;
	position: relative;
	text-align: center;
	transition: .2s all;
	position: relative;
	margin: 10px 20px 10px 0;
}

.find-me .item i {
	position: relative;
	display: inline-block;
	font-size: 26px;
	z-index: 2;
	transition: .3s all;
}

.find-me .item span {
	font-size: 12px;
	display: block;
	position: absolute;
	bottom: 0px;
	left: 0;
	width: 80px;
	height: auto;
	line-height: 1;
	z-index: 2;
	opacity: 0;
	transition: .3s all;
}

.find-me .item:hover span {
	opacity: .6;
	bottom: 16px;
}

.find-me .item:hover i {
	transform: translateY(-10px);
}

.find-me .item::after {
	content: '';
	display: block;
	position: absolute;
	bottom: 0;
	left: 0;
	height: 4px;
	width: 100%;
	background-color: #333;
	transition: .2s all;
	border-radius: 2px;
	z-index: 0;
}

.find-me .item:nth-child(1)::after {
	background-color: #66ccff;
}

.find-me .item:nth-child(2)::after {
	background-color: #0088cc;
}

.find-me .item:nth-child(3)::after {
	background-color: #F58930;
}

.find-me .item:nth-child(4)::after {
	background-color: #28a9e0;
}

.find-me .item:nth-child(5)::after {
	background-color: #f09199;
}

.find-me .item:nth-child(6)::after {
	background-color: #f12d35;
}

.find-me .item:nth-child(7)::after {
	background-color: #555;
}

.find-me .item:nth-child(8)::after {
	background-color: #b600ff;
}

.find-me .item:hover {
	transform: translateY(-3px);
}

.find-me .item:hover::after {
	height: 100%;
	border-radius: 10px;
}

.roll-tag {
	display: none;
	transition: .1s all;
	width: 100%;
	position: relative;
	height: 80px;
	margin-bottom: 10px;
}

.roll-tag.active {
	display: inline-block;
}

.roll-tag span {
	position: absolute;
	animation: tag-entering 0.1s ease-in-out;
	box-shadow: 0 0 20px rgba(0, 0, 0, .28);
	background-color: #333;
	border-radius: 10px;
	padding: 10px 20px;
	display: inline-block;
	white-space: nowrap;
	transform-origin: center left;
}

.roll-tag span::before {
	content: '#';
	margin-right: 5px;
	display: inline;
}

.roll-tag span.removing {
	animation: tag-removing 0.1s ease-in-out;
}

a[href="javascript:rollOnce()"] {
	position: relative;
	color: #fff;
	text-decoration: none;
}

a[href="javascript:rollOnce()"]::after {
	content: "";
	position: absolute;
	bottom: -3px;
	left: 0;
	width: 100%;
	height: 2px;
	background-color: #fff;
}
