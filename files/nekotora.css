@font-face {
  font-family: "neko-icon";
  src: url('nekotora.eot?t=1615226849119');
  /* IE9 */
  src: url('nekotora.eot?t=1615226849119#iefix') format('embedded-opentype'),
    /* IE6-IE8 */
    url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
    url('nekotora.woff?t=1615226849119') format('woff'),
    url('nekotora.ttf?t=1615226849119') format('truetype'),
    /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
    url('nekotora.svg?t=1615226849119#neko-icon') format('svg');
  /* iOS 4.1- */
}

.neko-icon {
  font-family: "neko-icon" !important;
  font-size: 26px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-steam:before {
  content: "\e602";
}

.icon-music:before {
  content: "\e601";
}

.icon-telegram:before {
  content: "\e65f";
}

.icon-email:before {
  content: "\e61d";
}

.icon-douban:before {
  content: "\e707";
}

.icon-wechat:before {
  content: "\e6b4";
}

.icon-weibo:before {
  content: "\e882";
}

.icon-bookmark:before {
  content: "\e780";
}

.icon-instagram:before {
  content: "\e6fc";
}

.icon-qq:before {
  content: "\e746";
}

.icon-bilibili:before {
  content: "\e75c";
}

.icon-twitter:before {
  content: "\e9b2";
}

.icon-link:before {
  content: "\e6c5";
}

.icon-github:before {
  content: "\e600";
}
