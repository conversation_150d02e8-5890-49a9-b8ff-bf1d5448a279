window.requestAnimFrame = function () { return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame || function (a) { window.setTimeout(a, 1E3 / 60) } }();


// 确保 DOM 加载完成后执行
document.addEventListener('DOMContentLoaded', function () {
    // 创建离屏canvas
    var bufferCanvas = document.createElement("canvas");
    var canvas = document.getElementById("startrack");

    if (!canvas) {
        console.error("Canvas element not found!");
        return;
    }

    // 设置canvas尺寸
    canvas.width = bufferCanvas.width = canvas.offsetWidth;
    canvas.height = bufferCanvas.height = canvas.offsetHeight;

    // 计算最长边，用于确定绘制范围
    var longside = Math.max(canvas.width, canvas.height);
    bufferCanvas.width = longside * 2.6;
    bufferCanvas.height = longside * 2.6;

    // 获取绘图上下文
    var ctx = canvas.getContext("2d");
    var bufferCtx = bufferCanvas.getContext("2d");

    var centerX = canvas.width;
    var stars = [];
    var drawTimes = 0;

    // 生成随机数
    function random(min, max) {
        var range = max - min;
        var rand = Math.random();
        return min + Math.round(rand * range);
    }

    // 初始化背景
    ctx.fillStyle = "rgba(21,21,21,1)";
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    ctx.lineCap = "round";

    // 创建星星数据
    for (var i = 20000; i--;) {
        var r = random(120, 255);
        var g = random(120, 255);
        var b = random(120, 255);
        var a = random(30, 100) / 100;

        stars.push({
            x: random(-bufferCanvas.width, bufferCanvas.width),
            y: random(-bufferCanvas.height, bufferCanvas.height),
            size: 1.2,
            color: "rgba(" + r + "," + g + "," + b + "," + a + ")"
        });
    }

    // 在离屏canvas上绘制星星
    function drawStars() {
        for (var i = stars.length; i--;) {
            var star = stars[i];
            bufferCtx.beginPath();
            bufferCtx.arc(star.x, star.y, star.size, 0, Math.PI * 2, true);
            bufferCtx.fillStyle = star.color;
            bufferCtx.closePath();
            bufferCtx.fill();
        }
    }
    drawStars();

    // 将离屏canvas内容绘制到主canvas
    function drawFromBuffer() {
        ctx.drawImage(bufferCanvas, -bufferCanvas.width / 2, -bufferCanvas.height / 2);
    }

    // 动画循环
    function loop() {
        drawFromBuffer();
        drawTimes++;

        if (drawTimes > 150 && drawTimes % 8 == 0) {
            ctx.fillStyle = "rgba(0,0,0,.04)";
            ctx.fillRect(-longside * 3, -longside * 3, longside * 6, longside * 6);
        }

        rotateCanvas(0.025);
    }

    // 旋转画布
    function rotateCanvas(deg) {
        ctx.rotate(deg * Math.PI / 180);
    }

    // 设置画布原点
    ctx.translate(centerX, 0);

    // 启动动画
    function animate() {
        requestAnimFrame(animate);
        loop();
    }
    animate();

    // 窗口大小改变时重置canvas
    window.onresize = function () {
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;
        ctx.fillStyle = "rgba(21,21,21,1)";
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.translate(centerX, 0); // 重置变换
    };

    // 滚动效果
    window.onscroll = function () {
        if (window.scrollY > window.innerHeight * 0.6) {
            document.querySelector(".background").classList.add("fixed");
        } else {
            document.querySelector(".background").classList.remove("fixed");
        }
    };

    // 更新标语
    function updateSlogan() {
        var slogans = [
            "当你在凝视着网页的时候<br>网页也正在凝视着你",
            "Talk is cheap.<br>Show me the code.",
            "不要重复自己。",
            "我们是否也能像星星一样，<br>在短暂的生命旅程中发出属于自己的光芒？",
            "浩瀚的星河静静流淌，<br>而我们只是其中一粒尘埃。",
            "你好！<br>Hello !",
            "宇宙中唯一不变的就是变化本身。",
            "简单即是美，<br>复杂是错误的温床。",
            "星空是时间的画布，<br>每一颗星都承载着亿万年的光辉。",
            "时间是最公正的，<br>它从不多给任何人一秒。",
        ];
        var index = random(0, slogans.length - 1);
        var sloganElement = document.getElementById("slogan");
        if (sloganElement) {
            sloganElement.innerHTML = slogans[index];
        }
    }

    updateSlogan();
    console.log("Welcome to xiadengma's Homepage");
});

// 你都来这里翻代码看所有标签了，看来是真爱。
var tags = [
    // 定义
    '生于浙江龙游',
    // 生活
    '熬夜大赛亚军（倒在太阳升起后）', 'NO麦当劳 NOKFC', '无糖可乐爱好者', '魔爪原味（冰）爱好者', '互联网冲浪选手',
    // 动画 & 游戏
    'RogueLike游戏高手', '魂类游戏爱好者', 'Minecraft 玩家', 'Steam 游戏收藏家',
    // 技术
    'VSCode 忠实用户', 'Arch Linux 忠实用户', 'Linux 多年用户', 'DL/CV 工程师', 'RoboMaster 机器人爱好者', '开源软件贡献者',
    // 设备&工具
    'ROG 用户', '二手爱好者',
    // 短句
    '你记住我了吗，当你试着多roll几个标签的时候，我就赢了',
    '刚刚走神了，这个不算，再roll一个',
    '你很幸运，roll到了这个毫无意义的标签，请再roll一个',
    '【疑似服务器出错】¥¹ÈÃÇK¥Ha±É8:E@s29t="`%',
    '你发现了一个彩蛋标签，请继续探索吧！'
];

let rollTimer = null;

function random(Min, Max) {
    var Range = Max - Min;
    var Rand = Math.random();
    var num = Min + Math.round(Rand * Range);
    return num;
}

function rollATag() {
    $(".roll-tag").addClass('active');
    let el = $(".roll-tag span.ready")
    el.addClass('removing');
    setTimeout(() => {
        el.remove();
    }, 100)
    var span = $("<span></span>").text(getRandomTag());
    $(".roll-tag").append(span);
    setTimeout(() => {
        span.addClass('ready');
    }, 100)
}

function rollOnce() {
    clearInterval(rollTimer)
    rollTimer = setInterval(rollATag, 20)
    setTimeout(function () {
        clearInterval(rollTimer)
        rollTimer = setInterval(rollATag, 40)
    }, 400)
    setTimeout(function () {
        clearInterval(rollTimer)
        rollTimer = setInterval(rollATag, 80)
    }, 800)
    setTimeout(function () {
        clearInterval(rollTimer)
        rollTimer = setInterval(rollATag, 140)
    }, 1200)
    setTimeout(function () {
        clearInterval(rollTimer)
        rollTimer = setInterval(rollATag, 240)
    }, 1600)
    setTimeout(function () {
        clearInterval(rollTimer)
        rollTimer = null
    }, 1800)
}

function getRandomTag() {
    return tags[random(0, tags.length - 1)];
}
